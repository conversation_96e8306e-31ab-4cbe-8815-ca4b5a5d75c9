<?php

namespace App\Services;

use App\CommonSetting;
use App\Models\EmailVerfication;
use App\Models\PasswordOtp;
use App\Models\PhoneVerification;
use App\Notifications\UserRegisterNotification;
use App\Profile;
use App\Traits\SendEmailTrait;
use App\User;
use App\Wallet;
use App\WithdrawalRequest;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\{Auth, Hash, Http, Mail, Validator};

class VerificationService
{
    use SendEmailTrait;
    private $clientId;
    private $clientSecret;
    function __construct()
    {
        $this->clientId = "tipalti.thirdpartyapi.FTeCrli-8e3rUbeQTMrzBh4FxKg";
        $this->clientSecret = "aSTyuU6SY0rjOzx0EcVzOYqyW_0";
    }
    public function send_otp($recipient, $type, $message_type = null, $otp_type = null)
    {
        try {
            $code = generate_otp(); // e.g., random 6-digit number
            $twilio = new \App\Services\TwilioService();
            if ($type === "phone") {
                if ($message_type == 'otp') {
                    if (auth()->user()->phone_verified_at == null) {
                        $message = "Welcome to LuxuStars!\nYour verification code is $code, valid for the next 15 minutes. For your security, do not share this code";
                    } else {
                        $message = "The verification code for your phone number change request is $code, valid for the next 15 minutes. For your security, do not share this code.";
                    }
                    $this->storeOtpData($type, $recipient, $code);
                } elseif ($message_type == 'info') {
                    $phone = PhoneVerification::latest()->first();
                    $updatedPhone = $phone->phone;
                    $message = "Security Alert!\nWe have sent you a verification code on your registered phone number $updatedPhone, valid for the next 15 minutes.";
                }

                // Send via WhatsApp or SMS based on otp_type
                if ($otp_type === 'whatsapp') {
                    $twilio->sendWhatsappSms($recipient, $message);
                } else {
                    $twilio->sendSms($recipient, $message);
                }
            } else {
                $this->storeOtpData($type, $recipient, $code);
                $email_template = ($type === "password") ? "forget_password" : "email_verfication";
                // $this->sendEmail($email_template, ["{{code}}"], [$code], $recipient);
                $place_holders = ["{{code}}"];
                $values = [$code ?? ""];
                (new \App\Services\NotificationService())->sendNotification("otp_request", $recipient, $place_holders, $values);
            }
            return ["status" => true, "message" => ucfirst($type) . " OTP sent!", "data" => null];
        } catch (\Exception $e) {
            return ["status" => false, "message" => $e->getMessage(), "data" => null];
        }
    }

    function storeOtpData($type, $recipient, $code)
    {
        $expiry_time = Carbon::now()->addMinutes(15);

        if ($type === 'email') {
            $model = EmailVerfication::class;
            $otp = $model::where('email', $recipient)->first() ?: new $model;
            $otp->email = $recipient;
            $otp->code = Hash::make($code);
            $otp->expire_date = $expiry_time;
            $otp->save();
        } elseif ($type === 'password') {
            $model = PasswordOtp::class;
            $otp = $model::where('email', $recipient)->first() ?: new $model;
            $otp->email = $recipient;
            $otp->code = Hash::make($code);
            $otp->expire_date = $expiry_time;
            $otp->is_verified = encrypt(0);
            $otp->save();
        } elseif ($type === 'phone') {
            $otp = PhoneVerification::where('phone', $recipient)->first() ?: new PhoneVerification;
            $otp->phone = $recipient;
            $otp->code = Hash::make($code);
            $otp->expire_date = $expiry_time;
            $otp->created_at = now();
            $otp->save();
        }
    }
    function change_email_otp($email)
    {
        $code = generate_otp();
        $this->storeOtpData("email", $email, $code);
        activity("User")->causedBy(auth()->id())->log("Verfication Email Send");
        return $this->sendEmail("email_verfication", ["{{code}}", "{{email}}"], [$code, $email], $email);
    }
    function forget_password($request_data)
    {
        $validator = Validator::make($request_data, [
            "email" => "required|email|exists:users,email",
        ]);
        if ($validator->fails()) {
            // activity("User")->causedBy($request_data["email"])->log($validator->errors()->first());
            return ["status" => false, "message" => $validator->errors()->first(), "data" => null];
        }
        return $this->send_otp($request_data["email"], "password", "email-templates.forget-password", "Forget Password");
    }

    function email_verify($request_data)
    {
        $validator = Validator::make($request_data, [
            "email" => "required|email",
        ]);
        if ($validator->fails()) {
            // activity("User")->causedBy($request_data["email"])->log($validator->errors()->first());
            return ["status" => false, "message" => $validator->errors()->first(), "data" => null];
        }
        return $this->send_otp($request_data["email"], "email", "email-templates.email-verify", "Email verification");
    }

    function verify_otp($request_data)
    {
        $validator = Validator::make($request_data, [
            "email" => "required|email",
            "otp" => "required",
            "type" => "required|in:email,password"
        ], [
            "type.in" => "The selected type is invalid. Allowed values are email or password."
        ]);
        if ($validator->fails()) {
            return ["status" => false, "message" => $validator->errors()->first(), "data" => null];
        }
        try {
            $user = User::where("email", $request_data["email"])->first();
            if (!$user) {
                return ["status" => false, "message" => "Email not exist", "data" => null];
            }
            $otpModel = $request_data['type'] == "email" ? EmailVerfication::class : PasswordOtp::class;
            $otp_verify = $otpModel::where("email", $user->email)->where("expire_date", ">=", now())->first();
            if (!$otp_verify || !Hash::check($request_data["otp"], $otp_verify->code)) {
                return ["status" => false, "message" => "Invalid OTP or OTP expired", "data" => null];
            }
            // OTP verification success
            if ($request_data['type'] == "email") {
                $user->email_verified = 1;
                $user->save();
                $otp_verify->delete();
                $place_holders = ["{{guest_first_name}}"];
                $values = [$user->first_name ?? ""];
                (new \App\Services\NotificationService())->sendNotification("welcome_to_luxustars", $request_data["email"] ?? $user->email, $place_holders, $values);
                activity("User")->causedBy($user->id)->log('otp matched and email verified');
                return ["status" => true, "message" => "OTP matched and email verified", "data" => null];
            } else {
                $otp_verify->is_verified = encrypt(1);
                $otp_verify->save();
                activity("User")->causedBy($user->id)->log('OTP matched! Now you can reset password');
                return ["status" => true, "message" => "OTP matched! Now you can reset password", "data" => null];
            }
        } catch (\Throwable $th) {
            return ["status" => false, "message" => $th->getMessage(), "data" => null];
        }
    }
    function sign_up_email_verification($email, $otp)
    {
        $otp_verify = EmailVerfication::where("email", $email)->where("expire_date", ">=", now())->first();
        if (!$otp_verify || !Hash::check($otp, $otp_verify->code)) {
            return ["status" => false, "message" => "Invalid OTP or OTP expired", "data" => null];
        }
        $user_data = session("user_data");
        $user_data["email_verified"] = 1;
        $user_data["name"] = $user_data["first_name"] . " " . $user_data["last_name"];
        $user_data["password"] = Hash::make($user_data["password"]);
        $user = User::create($user_data);
        if ($user) {
            $profile = new Profile();
            $profile->user_id = $user->id;
            $profile->country = $user_data["country"] ?? null;
            $profile->save();
            $user->assignRole('customer');
            $user->notify(new UserRegisterNotification("Congratulations! " . $user->name . " your account has been registered at LuxuStars"));
            admins_notification(new UserRegisterNotification("New Registration " . $user->name . " has been registered"));
        }
        $otp_verify->delete();
        session()->forget('user_data');
        Auth::attempt(["email" => $user->email, "password" => $user_data["password_confirmation"]]);
        activity("User")->causedBy($user->id)->log('otp matched and email verified');
        return ["status" => true, "message" => "OTP matched and email verified", "data" => null];
    }
    public function sign_up_phone_verification($phone, $otp)
    {
        $otp_verify = PhoneVerification::where("phone", $phone)->where("created_at", '>=', now()->subMinutes(5))->first();
        if (!$otp_verify || !Hash::check($otp, $otp_verify->code)) {
            return ["status" => false, "message" => "Invalid OTP or OTP expired", "data" => null];
        }

        return [
            "status" => true,
            "message" => "OTP matched and phone verified"
        ];
    }
    function wallet_verification($email, $otp, $status)
    {
        $otp_verify = EmailVerfication::where("email", $email)->where("expire_date", ">=", now())->first();
        if (!$otp_verify || !Hash::check($otp, $otp_verify->code)) {
            return ["status" => false, "message" => "Invalid OTP or OTP expired", "data" => null];
        }
        $user_data = session("wallet_data");
        if ($status == 1) {
            $user_data["schedule_amount"] = $user_data["schedule_amount"];
            $user_data["schedule_active"] = $user_data["schedule_active"];
            $user = User::where('email', $user_data['user_email'])
                ->update([
                    'schedule_amount' => $user_data['schedule_amount'],
                    'schedule_active' => $user_data['schedule_active']
                ]);
        } elseif ($status == 0) {
            $wallet = Wallet::where("user_id", auth()->id())->first();
            $this->refreshAccessToken();
            $accessToken = session('tipalti_access_token');
            if (!$accessToken) {
                return ["status" => false, "message" => "Missing Tipalti Access Token", "data" => null];
            }
            try {
                $reference_code = uniqid();
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ])->post('https://api-sb.tipalti.com/api/v1/payment-batches', [
                    'paymentInstructions' => [
                        [
                            'amountSubmitted' => [
                                'amount' => $user_data["amount"],   // Dynamic Amount
                                'currency' => 'COP',  // Dynamic Currency
                            ],
                            'refCode' => (string) $reference_code,  // Reference Code
                            'payeeId' =>  auth()->user()->payee_id  // Payee ID
                        ]
                    ]
                ]);
                $data = $response->json();
                $url = 'https://api-sb.tipalti.com/api/v1/payment-batches/' . $data['id'];
                //$url = 'https://api-sb.tipalti.com/api/v1/payment-batches/' . $data['id'] . '/instructions';
                $response = Http::withHeaders([
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $accessToken,
                ])->get($url);
                if ($response->successful()) {
                    $responseData = $response->json();
                    Log::info("Payment batches by id: " . json_encode($responseData));
                } else {
                    return ["status" => false, "message" => $response->status(), "data" => null];
                }
                //$url = 'https://api-sb.tipalti.com/api/v1/payment-batches/' . $data['id'];
                $url = 'https://api-sb.tipalti.com/api/v1/payment-batches/' . $responseData['id'] . '/instructions';
                $response = Http::withHeaders([
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $accessToken,
                ])->get($url);
                if ($response->successful()) {
                    $responseData = $response->json();
                    Log::info("Payment batches/instructions: " . json_encode($responseData));
                } else {
                    return ["status" => false, "message" => $response->status(), "data" => null];
                }
                if ($response->successful()) {
                    $withdrawal_request = new WithdrawalRequest();
                    $withdrawal_request->user_id = auth()->id();
                    $withdrawal_request->reference_code = $reference_code;
                    $withdrawal_request->currency = 'COP';
                    $withdrawal_request->amount = $user_data["amount"];
                    $withdrawal_request->wallet_amount = $wallet->amount;
                    $withdrawal_request->payment_method_type = auth()->user()->payment_method ?? 'N/A';
                    $withdrawal_request->status = 0;
                    $withdrawal_request->save();
                    $wallet->amount = $wallet->amount - $user_data["amount"];
                    $wallet->save();
                } else {
                    return ["status" => false, "message" => "Tipalti API Error: " . json_encode($data), "data" => null];
                }
            } catch (\Exception $e) {
                return ["status" => false, "message" => "Error: " . $e->getMessage(), "data" => null];
            }
        }
        // if ($user) {
        //     $profile = new Profile();
        //     $profile->user_id = $user->id;
        //     $profile->save();
        //     $user->assignRole('customer');
        //     $user->notify(new UserRegisterNotification("Congratulations! " . $user->name . " your account has been registered at LuxuStars"));
        //     admins_notification(new UserRegisterNotification("New Registration " . $user->name . " has been registered"));
        // }
        $otp_verify->delete();
        session()->forget('wallet_data');
        //activity("User")->causedBy($user->id)->log('otp matched and wallet Schedule verified');
        return ["status" => true, "message" => "OTP matched and Wallet schedule verified", "data" => null];
    }
    function verify_change_email($email, $code)
    {
        try {
            $otpVerification = EmailVerfication::where('email', $email)
                ->where('expire_date', '>=', now())
                ->first();

            if (!$otpVerification || !Hash::check($code, $otpVerification->code)) {
                return api_response(false, "The code you entered is not valid. Please enter the correct code.");
            }

            $otpVerification->delete();

            $user = User::find(auth()->id());
            $user->email = $email;
            $user->save();

            $data = [
                'name' => auth()->user()->name,
                'type' => 'email',
                'new_email' => $email,
                'date' => now()->toDateTimeString()
            ];

            // Send email
            // $this->sendEmail('success_email', ["{{name}}", "{{type}}", "{{new_email}}", "{{date}}"], [$data['name'], $data['type'], $data['new_email'], $data['date']], $email);
            Mail::send('email-templates.success-email', compact('data'), function ($message) use ($email) {
                $message->to($email)
                    ->subject('Email updated successfully');
            });
            return [
                'status' => true,
                'message' => 'OTP matched and email changed.',
                'data' => null
            ];
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage());
        }
    }

    function verify_change_phone($phone, $code)
    {
        try {
            $otp_verify = PhoneVerification::where("phone", $phone)->where("expire_date", ">=", now())->first();
            if (isset($otp_verify) && !empty($otp_verify) && Hash::check($code, $otp_verify->code)) {
                $otp_verify->delete();
                $user = User::find(auth()->id());
                $user->phone = $phone;
                $user->phone_verified_at = now();
                $user->save();
                //activity("User")->causedBy(auth()->id())->log('otp matched and phone changed');
                $data["name"] = auth()->user()->name;
                $data["type"] = "phone";
                $data["new_phone"] = $phone;
                $data["date"] = now()->toDateTimeString();
                return ["status" => true, "message" => "OTP matched and phone changed", "data" => null];
            } else {
                //activity("User")->causedBy(auth()->id())->log('The code you entered is not valid. Please enter the correct code');
                return ["status" => false, "message" => "The code you entered is not valid. Please enter the correct code", "data" => null];
            }
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage());
        }
    }
    public function refreshAccessToken()
    {
        $client = new Client();
        $refreshToken = CommonSetting::first()->refresh_token ?? '';
        if (!$refreshToken) {
            //return response()->json(["error" => "Refresh token is missing."], 400);
            return redirect()->route('tipalti.auth');
        }
        try {
            $response = $client->post('https://sso.sandbox.tipalti.com/connect/token', [
                'form_params' => [
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $refreshToken,
                ],
                'headers' => [
                    'Accept' => 'application/json',
                ],
            ]);

            $tokenData = json_decode($response->getBody(), true);
            session()->put('tipalti_access_token', $tokenData['access_token']);
            session()->put('tipalti_refresh_token', $tokenData['refresh_token']);
            session()->put('tipalti_token_expires_in', now()->addSeconds($tokenData['expires_in']));
            return response()->json(["message" => "Access token refreshed successfully", "token" => $tokenData]);
        } catch (\Exception $e) {
            return response()->json(["error" => "Failed to refresh access token", "message" => $e->getMessage()], 400);
        }
    }
}
