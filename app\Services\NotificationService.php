<?php

namespace App\Services;

use App\Booking;
use App\Mail\BookingCreateMail;
use App\Models\NotificationTemplate;
use App\Notifications\InAppNotification;
use App\User;
use Illuminate\Support\Facades\Mail;

class NotificationService
{
    protected $twilioService;
    function __construct()
    {
        $this->twilioService = new TwilioService();
    }
    public function sendNotification($key, $email, array $placeholders, array $values)
    {
        $template = NotificationTemplate::where('key', $key)->first();
        if (!$template) {
            return;
        }
        $user = User::where("email", $email)->first();
        $email = $user->email ?? $email;

        if (!$email) {
            return;
        }
        // Replace placeholders in the message
        $short = $this->replacePlaceholders($template->short, $placeholders, $values);
        $expanded = $this->replacePlaceholders($template->expanded, $placeholders, $values);
        $template->short = $short;
        $template->expanded = $expanded;
        // $message = $this->replacePlaceholders($template->message, $placeholders, $values);

        // Send in-app notification if enabled
        if ($template->is_active && $template->app) {
            if($user){
                $user->notify(new InAppNotification($template));
            }
        }

        // Send email notification if enabled
        if ($template->is_active && $template->email) {
            if($key == "booking_confirmed"){
                $booking = Booking::with("detail")->where("user_id", $user->id)->latest()->first();
                Mail::to($email)->send(new BookingCreateMail($booking));
            }else{
                Mail::send('email-templates.notification-template', compact("template"), function ($message) use ($email, $template) {
                    $message->to($email)
                        ->subject($template->title);
                });
            }
        }

        // Send SMS notification if enabled
        if ($template->is_active && $template->sms) {
            // $twilio = new TwilioService();
            if(isset($user->phone) && !empty($user->phone)){
                $this->twilioService->sendSms($user->phone, $short);
            }
        }

        // Send WhatsApp notification if enabled
        // if ($template->is_active && $template->whatsapp) {
        //     // $twilio = new TwilioService();
        //     $this->twilioService->sendWhatsappSms($user->phone, $short);
        // }

        // Optionally, you can add logic for other channels like SMS, Push notifications, etc.
        // Example for SMS or other channels can be added here.
    }

    private function replacePlaceholders($message, $placeholders, $values)
    {
        return str_replace($placeholders, $values, $message);
    }
}
