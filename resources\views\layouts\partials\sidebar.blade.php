<aside class="sidebar">
    <div class="scroll-sidebar">
        @if (session()->get('theme-layout') != 'fix-header')
            <!-- <div class="user-profile">
                <div class="dropdown user-pro-body ">
                    <div class="profile-image">
                        @if (auth()->user()->profile->pic == null)
<img src="{{ asset('storage/uploads/users/no_avatar.jpg') }}" alt="user-img" class="img-circle">
@else
<img src="{{ asset('storage/uploads/users/' . auth()->user()->profile->pic) }}" alt="user-img" class="img-circle">
@endif
                        <a href="javascript:void(0);" class="dropdown-toggle u-dropdown text-blue"
                           data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                        <span class="badge badge-danger">
                            <i class="fa fa-angle-down"></i>
                        </span>
                        </a>
                        <ul class="dropdown-menu animated flipInY">
                            {{-- <li><a href="{{url('profile')}}"><i class="fa fa-user"></i> Profile</a></li> --}}
                            {{-- <li><a href="javascript:void(0);"><i class="fa fa-inbox"></i> Inbox</a></li> --}}
                            <li role="separator" class="divider"></li>
                            <li><a href="{{ url('account-settings') }}"><i class="fa fa-cog"></i> Account Settings</a></li>
                            <li role="separator" class="divider"></li>
                            <li><a href="{{ url('logout') }}"><i class="fa fa-power-off"></i> Logout</a></li>
                        </ul>
                    </div>
                    <p class="profile-text m-t-15 font-16">
                        <a href="javascript:void(0);">
                            @if (auth()->user()->name == 'Admin')
Developer
@elseif(auth()->user()->name == 'User')
Admin
@else
{{ auth()->user()->name }}
@endif
                        </a>
                    </p>
                </div>
            </div> -->
        @endif
        <nav class="sidebar-nav">
            <ul id="side-menu">
                <li>
                    <a class="active waves-effect" href="{{ url('dashboard') }}" aria-expanded="false"><i
                            class="fa-solid fa-house"></i><span class="hide-menu">
                            {{ translate('dashboard_sidebar.dashboard') }} </span></a>
                    @if (auth()->user()->isAdmin() == true)
                        <ul aria-expanded="false" class="collapse">
                            <li><a href="{{ asset('dashboard') }}">Modern Version</a></li>
                            <li><a href="{{ asset('index2') }}">Clean Version</a></li>
                            <li><a href="{{ asset('index3') }}">Analytical Version</a></li>
                            <li>
                                <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><span
                                        class="hide-menu"> eCommerce </span></a>
                                <ul aria-expanded="false" class="collapse">
                                    <li><a href="{{ asset('index4') }}">Dashboard</a></li>
                                    <li><a href="{{ asset('products') }}">Products</a></li>
                                    <li><a href="{{ asset('product-detail') }}">Product Detail</a></li>
                                    <li><a href="{{ asset('product-edit') }}">Product Edit</a></li>
                                    <li><a href="{{ asset('product-orders') }}">Product Orders</a></li>
                                    <li><a href="{{ asset('product-cart') }}">Product Cart</a></li>
                                    <li><a href="{{ asset('product-checkout') }}">Product Checkout</a></li>
                                </ul>
                            </li>
                        </ul>
                    @endif
                </li>
                @if (auth()->user()->hasRole('user'))
                    <li class="{{ request()->is('e-wallet') ? 'active' : '' }}">
                        <a class="waves-effect {{ request()->is('e_wallet') ? 'active' : '' }}"
                            href="{{ route('e_wallet') }}">
                            <i class="fa-solid fa-wallet" style="color: #4a4a4a;"></i>
                            <span class="hide-menu">{{ translate('dashboard_sidebar.e_wallet') }} </span>
                        </a>
                    </li>
                    {{-- <li>
                            <a class="waves-effect" href="{{route('listings')}}">
                                <i class="fa-regular fa-file-lines" style="color: #4a4a4a;"></i>
                                <span class="hide-menu"> Listings </span>
                            </a>
                        </li> --}}
                    {{-- <li>
                            <a class="waves-effect" href="{{route('booking')}}">
                                <i class="fa-regular fa-file-lines" style="color: #4a4a4a;"></i>
                                <span class="hide-menu"> Bookings </span>
                            </a>
                        </li> --}}
                    {{-- <li> --}}
                    {{-- <a class="waves-effect" href="#"> --}}
                    {{-- <i class="fa-solid fa-file-invoice" style="color: #ffffff;"></i> --}}
                    {{-- <span class="hide-menu"> Calendar </span> --}}
                    {{-- </a> --}}
                    {{-- </li> --}}
                    {{-- <li>
                        <a class="waves-effect" href="{{ route('Notification') }}">
                            <i class="fa-solid fa-bell" style="color: #ffffff;"></i>
                            <span class="hide-menu"> Notifications </span>
                        </a>
                    </li> --}}
                    {{-- <li>
                            <a class="waves-effect" href="{{route('setting')}}">
                                <i class="fa-solid fa-sliders" style="color: #4a4a4a;"></i>
                                <span class="hide-menu"> Settings </span>
                            </a>
                        </li> --}}
                    <li class="{{ request()->is('user-management*') ? 'active' : '' }}">
                        <a class="waves-effect  {{ request()->is('user-management*') ? 'active' : '' }}"
                            href="{{ route('user_management') }}">
                            <i class="fa-solid fa-user-gear" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.user_management') }} </span>
                        </a>
                    </li>


                    {{-- message --}}
                    <li>
                        <a class="waves-effect position-relative" href="{{ route('message.dashboard_messenger') }}">
                            <i class="fas fa-envelope" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.messages') }} </span>
                            {{-- message count --}}
                            <div class="badge position-absolute" id="messages-sidebar-badge"
                                style="display: {{ auth()->user()->unread_messages_count() > 0 ? 'block' : 'none' }};">
                                <p class="fs-12" id="messages-sidebar-count">
                                    {{ auth()->user()->unread_messages_count() > 9 ? '9+' : auth()->user()->unread_messages_count() }}
                                </p>
                            </div>
                            {{-- message count end --}}
                        </a>
                    </li>
                    {{-- message end  --}}


                    <li class="@if (request()->is('cms*') ||
                            request()->is('*amenity-option*') ||
                            request()->is('amenities*') ||
                            request()->is('*listing-type*')) active @endif">
                        <a class="waves-effect" href="{{ route('cms') }}">
                            <i class="fas fa-th-large" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.cms') }} </span>
                        </a>
                    </li>
                    {{-- <li>
                        <a class="waves-effect" href="{{ route('language') }}">
                            <i class="fas fa-language" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> {{ __('language') }} </span>
                        </a>
                    </li> --}}
                    <li>
                        <a class="waves-effect" href="{{ route('user_chats') }}">
                            <i class="fas fa-comment-alt" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.user_chat') }} </span>
                        </a>
                    </li>
                    <li>
                        <a class="waves-effect" href="{{ route('contact_message') }}">
                            <i class="fas fa-headset" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.inbox') }} </span>
                            <div class="badge position-absolute">
                                <p class="fs-12" id="wishlist_count">
                                    {{ App\Models\ContactInfo::where('status', 0)->count() }}
                                </p>
                            </div>
                        </a>
                    </li>
                    {{-- <li>
                        <a class="waves-effect" href="{{ route("calender") }}">
                            <i class="fa-solid fa-calendar" style="color: #ffffff;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.calendar') }}</span>
                        </a>
                    </li> --}}
                    {{-- <li>
                        <a class="waves-effect" href="javascript:void(0);" aria-expanded="false">
                            <i class="fa-solid fa-user" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> CMS </span></a>
                        <ul aria-expanded="false" class="collapse">
                            <li><a href="{{ url('common_cms') }}" class="colorWhite">Common CMS</a></li>
                            <li><a href="{{ url('about_cms') }}">About Us</a></li>
                            <li><a href="{{ url('contact_cms') }}">Contact Us</a></li>
                        </ul>
                    </li> --}}
                    {{-- <li>
                        <a class="waves-effect" href="{{ route('report') }}">
                            <i class="fa-solid fa-user-shield" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> Report  </span>
                        </a>
                    </li> --}}
                @endif
                @if (auth()->user()->hasRole('service'))
                    <li class="{{ request()->is('e-wallet') ? 'active' : '' }}">
                        <a class="waves-effect {{ request()->is('e_wallet') ? 'active' : '' }}"
                            href="{{ route('e_wallet') }}">
                            <i class="fa-solid fa-wallet" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.e_wallet') }} </span>
                        </a>
                    </li>
                    <li>
                        <a class="waves-effect" href="{{ route('Notification') }}">
                            <i class="fa-solid fa-bell" style="color: #ffffff;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.notifications') }} </span>
                        </a>
                    </li>
                    <li>
                        <a class="waves-effect" href="{{ route('calender') }}">
                            <i class="fa-solid fa-calendar" style="color: #ffffff;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.calendar') }} </span>
                        </a>
                    </li>
                @endif
                @if (auth()->user()->isAdmin() == true)
                    <li><a class="waves-effect" href="{{ asset('role-management') }}">
                            <i class=" icon-layers fa-fw"></i><span class="hide-menu"> Roles </span></a>
                    </li>
                    <li class="two-column">
                        <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i
                                class="icon-user fa-fw"></i> <span class="hide-menu"> Users</span></a>
                        <ul aria-expanded="false" class="collapse">
                            <li><a href="{{ asset('users') }}">Manage Users</a></li>
                            <li><a href="{{ asset('user/create') }}">Add New User</a></li>
                            <li><a href="{{ asset('user/deleted') }}">Deleted Users</a></li>
                        </ul>
                    </li>
                    <li>
                        <hr />
                    </li>
                    {{-- <li><a class="waves-effect" href="{{asset('permission-management')}}"> <i --}}
                    {{-- class="icon-list fa-fw"></i><span class="hide-menu"> Permissions</span></a></li> --}}
                    <li><a class="waves-effect" href="{{ asset('crud-generator') }}">
                            <i class="icon-drawar fa-fw"></i><span class="hide-menu"> CRUD Generator</span></a>
                    </li>
                    <li class="two-column">
                        <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i
                                class="icon-eye fa-fw"></i> <span class="hide-menu"> Logs</span></a>
                        <ul aria-exzpanded="false" class="collapse">
                            <li><a href="{{ asset('log-viewer') }}">Laravel Log</a></li>
                            <li><a href="{{ asset('activity-log') }}">Activity Log</a></li>
                        </ul>
                    </li>
                @endif
                @foreach ($laravelAdminMenus->menus as $section)
                    @if (count(collect($section->items)) > 0)
                        @foreach ($section->items as $menu)
                            @can('view-' . str_slug($menu->title))
                                @if ($menu->title == 'WithdrawalRequest')
                                    @continue
                                @endif
                                @php
                                    $old = $menu->title;
                                @endphp
                                @php
                                    if ($menu->title == 'Listing') {
                                        $menu->title = translate('dashboard_sidebar.listings') ?? 'Listings';
                                        $menu->icon = 'fas fa-file-invoice-dollar';
                                    }
                                    if ($menu->title == 'Category') {
                                        $menu->title = translate('dashboard_sidebar.categories') ?? 'Categories';
                                        $menu->icon = 'fa-solid fa-list';
                                    }
                                    if ($menu->title == 'Booking') {
                                        $menu->title = translate('dashboard_sidebar.bookings') ?? 'Bookings';
                                        $menu->icon = 'fas fa-tag';
                                    }
                                    // if ($menu->title == 'WithdrawalRequest') {
                                    //     $menu->title = 'Withdrawal Requests';
                                    //     $menu->icon = 'fa-solid fa-money-bill-transfer';
                                    // }
                                    if ($menu->title == 'Report') {
                                        $menu->title = translate('dashboard_sidebar.reports') ?? 'Reports';
                                        $menu->icon = 'fa-solid fa-flag';
                                    }
                                    if ($menu->title == 'Review') {
                                        $menu->title = translate('dashboard_sidebar.reviews') ?? 'Reviews';
                                        $menu->icon = 'fas fa-star';
                                    }
                                    if ($menu->title == 'SubAdmin') {
                                        $menu->title = translate('dashboard_sidebar.sub_admin') ?? 'SubAdmin';
                                        $menu->icon = 'fa-solid fa-user';
                                    }
                                    if ($menu->title == 'AmenityOption') {
                                        // $menu->title = translate('dashboard_sidebar.amenity_options') ?? 'AmenityOption';
                                        $menu->icon = 'fas fa-volleyball-ball';
                                    }
                                    if ($menu->title == 'HelpCenter') {
                                        $menu->title = translate('dashboard_sidebar.help_center') ?? 'HelpCenter';
                                        $menu->icon = 'fas fa-question';
                                    }
                                    if ($menu->title == 'EmailTemplate') {
                                        $menu->icon = 'fas fa-envelope-open-text';
                                    }
                                @endphp
                                @if ($menu->title == 'AmenityOption')
                                    @continue
                                @endif
                                <li class="{{ request()->is(camel_case($old) . '/*') ? 'active' : '' }}">
                                    <a class="waves-effect" href="{{ url($menu->url) }}">
                                        {{-- <i class="glyphicon {{$menu->icon}} fa-fw"></i> --}}
                                        {{-- <i class="fa-regular fa-file-lines" style="color: #4a4a4a;"></i> --}}
                                        <i class=" {{ $menu->icon }}" style="color: #4a4a4a;"></i>


                                        <span class="hide-menu">
                                            {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', $menu->title) }}
                                        </span>
                                    </a>
                                </li>
                            @endcan
                        @endforeach
                    @endif
                @endforeach
                @if (auth()->user()->hasRole('user') || auth()->user()->hasRole('admin'))
                    <li class="{{ request()->is('languages') ? 'active' : '' }}">
                        <a class="waves-effect {{ request()->is('languages') ? 'active' : '' }}"
                            href="{{ route('languages.index') }}">
                            <i class="fa-solid fa-language" style="color: #4a4a4a;"></i>
                            <span class="hide-menu">{{ translate('dashboard_sidebar.languages') }} </span>
                        </a>
                    </li>
                    <li class="">
                        <a href="{{ route('notification-templates.index') }}" class="waves-effect">
                            <i class="fa fa-bell"></i>
                            <span class="hide-menu">Notification Templates</span>
                        </a>
                    </li>
                @endif
                {{-- <div class="side_btn">
                        <a class="btn-button" href="{{url('logout')}}" >
                            <i class="fa-solid fa-right-from-bracket"></i>Logout </a>
                    </div> --}}
                <!-- <li>
                    <a class="waves-effect" href="{{ url('account-settings') }}">
                        <i class="fa fa-gear fa-fw"></i>
                        <span class="hide-menu"> Account Settings</span>
                    </a>
                </li> -->
                {{-- @if (auth()->user()->hasRole('customer') || auth()->user()->hasRole('service'))
                    <li>
                        <a class="waves-effect position-relative" href="{{ route('dashboard_inbox') }}">
                            <i class="fas fa-envelope" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> {{ __('inbox') }}aa </span>

                            @php
                                $ch_msg = App\Models\ChMessage::where('to_id', auth()->id())->where('seen', 0)->count();
                            @endphp
                            @if ($ch_msg > 0)
                                <div class="badge position-absolute">
                                    <p class="fs-12" id="wishlist_count">
                                        {{ $ch_msg }}
                                    </p>
                                </div>
                            @endif
                        </a>
                    </li>
                @endif --}}
                @if (auth()->user()->isAdmin() == true)
                    <li>
                        <hr />
                    </li>
                    <li class="two-column">
                        <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i
                                class="icon-equalizer fa-fw"></i> <span class="hide-menu"> UI Elements</span></a>
                        <ul aria-expanded="false" class="collapse">
                            <li><a href="{{ asset('panels-wells') }}">Panels and Wells</a></li>
                            <li><a href="{{ asset('panel-ui-block') }}">Panels With BlockUI</a></li>
                            <li><a href="{{ asset('portlet-draggable') }}">Draggable Portlet</a></li>
                            <li><a href="{{ asset('buttons') }}">Buttons</a></li>
                            <li><a href="{{ asset('tabs') }}">Tabs</a></li>
                            <li><a href="{{ asset('modals') }}">Modals</a></li>
                            <li><a href="{{ asset('progressbars') }}">Progress Bars</a></li>
                            <li><a href="{{ asset('notification') }}">Notifications</a></li>
                            <li><a href="{{ asset('carousel') }}">Carousel</a></li>
                            <li><a href="{{ asset('user-cards') }}">User Cards</a></li>
                            <li><a href="{{ asset('timeline') }}">Timeline</a></li>
                            <li><a href="{{ asset('timeline-horizontal') }}">Horizontal Timeline</a></li>
                            <li><a href="{{ asset('range-slider') }}">Range Slider</a></li>
                            <li><a href="{{ asset('ribbons') }}">Ribbons</a></li>
                            <li><a href="{{ asset('steps') }}">Steps</a></li>
                            <li><a href="{{ asset('session-idle-timeout') }}">Session Idle Timeout</a></li>
                            <li><a href="{{ asset('session-timeout') }}">Session Timeout</a></li>
                            <li><a href="{{ asset('bootstrap-ui') }}">Bootstrap UI</a></li>
                        </ul>
                    </li>
                    <li class="two-column">
                        <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i
                                class="icon-docs fa-fw"></i> <span class="hide-menu"> Pages</span></a>
                        <ul aria-expanded="false" class="collapse">
                            <li><a href="{{ asset('starter-page') }}">Starter Page</a></li>
                            <li><a href="{{ asset('blank') }}">Blank Page</a></li>
                            <li><a href="{{ asset('search-result') }}">Search Result</a></li>
                            <li><a href="{{ asset('custom-scroll') }}">Custom Scrolls</a></li>
                            <li><a href="{{ asset('login') }}">Login Page</a></li>
                            <li><a href="{{ asset('lock-screen') }}">Lock Screen</a></li>
                            <li><a href="{{ asset('recoverpw') }}">Recover Password</a></li>
                            <li><a href="{{ asset('animation') }}">Animations</a></li>
                            <li><a href="{{ asset('profile') }}">Profile</a></li>
                            <li><a href="{{ asset('invoice') }}">Invoice</a></li>
                            <li><a href="{{ asset('gallery') }}">Gallery</a></li>
                            <li><a href="{{ asset('pricing') }}">Pricing</a></li>
                            <li><a href="{{ asset('register') }}">Register</a></li>
                            <li><a href="{{ asset('400') }}">Error-400</a></li>
                            <li><a href="{{ asset('403') }}">Error-403</a></li>
                            <li><a href="{{ asset('404') }}">Error-404</a></li>
                            <li><a href="{{ asset('500') }}">Error-500</a></li>
                            <li><a href="{{ asset('503') }}">Error-503</a></li>
                        </ul>
                    </li>
                    <li>
                        <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i
                                class="icon-notebook fa-fw"></i> <span class="hide-menu"> Forms </span></a>
                        <ul aria-expanded="false" class="collapse">
                            <li><a href="{{ asset('form-basic') }}">Basic Forms</a></li>
                            <li><a href="{{ asset('form-layout') }}">Form Layout</a></li>
                            <li><a href="{{ asset('icheck-control') }}">Icheck Control</a></li>
                            <li><a href="{{ asset('form-advanced') }}">Form Addons</a></li>
                            <li><a href="{{ asset('form-upload') }}">File Upload</a></li>
                            <li><a href="{{ asset('form-dropzone') }}">File Dropzone</a></li>
                            <li><a href="{{ asset('form-pickers') }}">Form-pickers</a></li>
                        </ul>
                    </li>
                    <li>
                        <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i
                                class="icon-grid fa-fw"></i> <span class="hide-menu"> Tables</span></a>
                        <ul aria-expanded="false" class="collapse">
                            <li><a href="{{ asset('basic-table') }}">Basic Tables</a></li>
                            <li><a href="{{ asset('table-layouts') }}">Table Layouts</a></li>
                            <li><a href="{{ asset('data-table') }}">Data Table</a></li>
                            <li><a href="{{ asset('bootstrap-tables') }}">Bootstrap Tables</a></li>
                            <li><a href="{{ asset('responsive-tables') }}">Responsive Tables</a></li>
                            <li><a href="{{ asset('editable-tables') }}">Editable Tables</a></li>
                        </ul>
                    </li>
                    <li>
                        <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i
                                class="icon-layers fa-fw"></i> <span class="hide-menu"> Extra</span></a>
                        <ul aria-expanded="false" class="collapse extra">
                            <li>
                                <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><span
                                        class="hide-menu"> Inbox </span></a>
                                <ul aria-expanded="false" class="collapse">
                                    <li><a href="{{ asset('inbox') }}">Mail Box</a></li>
                                    <li><a href="{{ asset('inbox-detail') }}">Mail Details</a></li>
                                    <li><a href="{{ asset('compose') }}">Compose Mail</a></li>
                                    <li><a href="{{ asset('contact') }}">Contact</a></li>
                                    <li><a href="{{ asset('contact-detail') }}">Contact Detail</a></li>
                                </ul>
                            </li>
                            <li>
                                <a href="{{ asset('calendar') }}" aria-expanded="false"><span
                                        class="hide-menu">Calendar</span></a>
                            </li>
                            <li>
                                <a href="{{ asset('widgets') }}" aria-expanded="false"><span class="hide-menu">
                                        Widgets </span></a>
                            </li>
                            <li>
                                <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><span
                                        class="hide-menu"> Charts</span></a>
                                <ul aria-expanded="false" class="collapse">
                                    <li><a href="{{ asset('morris-chart') }}">Morris Chart</a></li>
                                    <li><a href="{{ asset('peity-chart') }}">Peity Charts</a></li>
                                    <li><a href="{{ asset('knob-chart') }}">Knob Charts</a></li>
                                    <li><a href="{{ asset('sparkline-chart') }}">Sparkline charts</a></li>
                                </ul>
                            </li>
                            <li>
                                <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><span
                                        class="hide-menu"> Icons</span></a>
                                <ul aria-expanded="false" class="collapse">
                                    <li><a href="{{ asset('simple-line') }}">Simple Line</a></li>
                                    <li><a href="{{ asset('fontawesome') }}">Fontawesome</a></li>
                                </ul>
                            </li>
                            <li>
                                <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><span
                                        class="hide-menu"> Maps</span></a>
                                <ul aria-expanded="false" class="collapse">
                                    <li><a href="{{ asset('map-google') }}">Google Map</a></li>
                                    <li><a href="{{ asset('map-vector') }}">Vector Map</a></li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                @endif
                @if (auth()->user()->hasRole('customer') || auth()->user()->hasRole('service'))
                    <li>
                        <a class="waves-effect position-relative" href="{{ route('message.dashboard_messenger') }}">
                            <i class="fas fa-envelope" style="color: #4a4a4a;"></i>
                            <span class="hide-menu"> {{ translate('dashboard_sidebar.messages') }} </span>


                            <div class="badge position-absolute " id="messages-sidebar-badge"
                                style="display: {{ auth()->user()->unread_messages_count() > 0 ? 'block' : 'none' }};">
                                <p class="" class=" fs-12 " id="messages-sidebar-count">
                                    {{ auth()->user()->unread_messages_count() > 9 ? '9+' : auth()->user()->unread_messages_count() }}
                                </p>
                            </div>
                        </a>
                    </li>
                @endif
            </ul>
        </nav>
    </div>
</aside>
